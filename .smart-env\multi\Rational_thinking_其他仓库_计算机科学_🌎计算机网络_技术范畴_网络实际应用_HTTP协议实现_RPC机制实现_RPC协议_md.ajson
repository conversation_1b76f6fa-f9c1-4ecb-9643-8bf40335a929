"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07163137,-0.02718851,0.00032567,-0.05419776,-0.00740623,-0.01488455,-0.0200272,0.03159269,0.03783019,0.0202372,0.00436091,0.00157314,0.07526983,0.0482422,0.069562,0.00372336,-0.01450499,0.03212509,0.00461339,0.04799471,0.0827103,-0.02104541,-0.02749822,-0.05375473,0.02664329,0.04101408,0.0031888,-0.0242663,-0.0101897,-0.15931202,0.01912205,0.02196709,0.00017729,0.03354497,0.00259966,0.00428961,0.0266999,0.02455757,-0.03358293,0.01398993,-0.00170538,-0.05451404,0.01152026,-0.01224824,-0.02842634,-0.08694559,-0.01445655,-0.04260777,0.05534267,-0.01735439,-0.0606499,-0.04355598,-0.07881957,-0.02432081,-0.00128716,-0.03805584,0.051472,0.02071131,0.08482765,-0.07223813,0.04474401,0.04300618,-0.2276973,0.06891785,0.02664104,-0.02858536,0.00709375,-0.0342214,0.04088838,-0.0023585,-0.04771248,0.0510856,-0.02008607,0.06784165,0.04021601,-0.0277408,-0.01505398,-0.00742072,-0.01624909,-0.05800552,-0.03518982,0.02515552,-0.07124358,-0.01104528,0.0186721,0.03094389,-0.03171563,-0.01227349,0.02710673,0.0082781,-0.02681168,-0.01365603,0.04337997,0.02599608,-0.04826013,0.01927901,0.03698268,0.07130962,-0.07694581,0.10465477,-0.03541133,0.00223473,-0.0312371,-0.06644001,0.01793297,-0.04578814,-0.03242393,-0.01197274,-0.03696417,0.09547403,-0.04347008,-0.08087777,0.03962507,-0.05749781,0.04769315,0.05500964,0.00753376,0.03300015,-0.0498592,0.00452762,-0.04315956,0.00816617,0.07696231,-0.00726368,-0.01050537,-0.05579907,0.0479189,0.05015337,0.02507956,0.03803283,0.06580661,-0.01883416,-0.01763537,-0.0167771,-0.01116839,0.02681391,0.00552446,-0.01020866,-0.03258677,-0.03338793,-0.01310145,-0.06281965,0.01299768,-0.05912506,-0.03314607,0.06786524,-0.06372102,0.03394962,0.00376679,-0.05867043,0.04094608,0.06047456,-0.00596894,-0.04490224,-0.04589942,-0.0012257,0.07218514,0.13061912,-0.04556448,-0.0071204,0.00285765,-0.03924634,-0.05204318,0.12942807,0.05491962,-0.10675576,-0.00916232,-0.00727107,0.0099781,-0.01854977,0.03979147,-0.03461765,0.05021388,-0.05791024,0.04009612,0.00033068,-0.03529773,-0.03205688,0.02722782,-0.00759933,0.03097414,-0.02879078,-0.03489409,-0.0070398,-0.00184075,-0.08998224,-0.04367487,-0.02189085,0.02071555,-0.06412486,-0.10210832,0.01039425,-0.02496733,0.02330518,-0.00067756,-0.0412196,0.0322523,-0.00892319,0.03105534,-0.00394023,0.07992398,0.04367603,-0.01716519,-0.04933836,-0.06836864,-0.03250279,-0.00191051,0.04024559,0.0141183,0.07839684,0.01744754,0.05827929,0.00821667,0.05907299,-0.08358327,0.03572579,0.00748542,0.04020009,0.07750349,0.05339228,0.02808439,0.03555338,-0.06078142,-0.22464748,0.00645975,0.0585821,-0.03496764,0.03177192,-0.00070154,0.0500892,0.03504124,0.05012287,0.04909883,0.1433952,0.02564633,-0.03911418,-0.0021692,0.02439426,0.00704581,0.06192805,-0.03416543,-0.0648426,0.02069134,-0.01654231,0.0503748,-0.02561714,0.0425226,0.06143333,-0.01019974,0.10089099,-0.00828881,0.06531645,-0.00744723,0.08364901,-0.00631533,0.01414845,-0.12753706,-0.02972311,0.054813,-0.00906515,0.02078066,0.01423692,0.00628092,-0.03601383,0.04669692,-0.00399878,-0.08571418,-0.00023525,-0.032789,-0.04681602,-0.0107196,0.02790372,0.00483433,-0.00036148,0.00772331,0.02162099,-0.02240322,0.02007064,-0.04205516,-0.04628031,-0.05952746,0.01634568,0.03364267,0.05801874,0.01328706,0.00263008,-0.00203464,0.0308846,-0.07142828,0.02621337,0.03458304,-0.00189923,-0.03062774,-0.02782252,0.13329442,0.0084391,0.00259172,0.03158793,-0.0358532,-0.00125228,-0.06069812,-0.01540616,-0.05029972,0.07060098,-0.04432099,0.01126899,-0.02042825,-0.00756115,-0.00871792,0.01759313,0.01223582,0.03181694,-0.06385096,-0.0484329,-0.00797561,-0.0396921,0.05152902,0.0585348,0.00486723,-0.30395898,-0.00099236,-0.04112466,-0.00531034,0.02111336,0.02505792,0.06549044,-0.01212064,-0.0638779,0.03004126,-0.06393146,0.0668633,-0.03446285,-0.02360511,0.00886813,0.00420259,0.06084689,0.00506837,0.03110166,-0.08646768,-0.01653787,0.06291728,0.1847185,-0.00818782,0.08204412,0.03339183,-0.02175678,0.03429126,0.0195136,0.03822045,0.00542489,-0.04996029,-0.03510289,-0.08636104,0.05673297,0.04034197,0.00981909,0.01172953,0.01697414,-0.03244792,-0.05494159,0.05876148,-0.01737483,0.00404276,0.09839251,-0.01116305,-0.03076928,-0.03738828,0.05141411,0.07362703,-0.00382385,0.05928607,0.01046534,-0.01538008,0.03055319,0.02603669,0.02777484,-0.04303941,-0.0902504,-0.01648292,0.02355342,-0.03866062,0.10642567,0.05731089,0.0465605],"last_embed":{"hash":"d5e863e3b80f35a1cb72d5935628016c81cea934c7f1cd350cc45de5da470036","tokens":463}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.08668999,-0.00157813,0.01850887,0.00211984,0.00740805,0.0188796,0.01787624,-0.03235561,0.00245754,-0.01189781,0.01579633,0.03235987,-0.01910622,-0.05705259,0.01267158,0.04458245,-0.02009462,0.01151659,0.04086521,-0.03327732,-0.00696689,-0.02215198,-0.00966235,-0.00460994,-0.00493853,0.03394785,-0.04675266,-0.04976302,0.00422675,0.015836,0.05494194,-0.01823898,0.05626711,-0.01909724,0.00442577,0.02956172,0.00052085,0.02358423,-0.00346811,-0.03298507,-0.01439714,-0.00147854,-0.0297751,0.02072363,-0.09010463,0.00598548,0.01558194,0.0053896,0.00470762,0.02246787,0.02027106,0.03961234,0.01205396,-0.03459041,0.0171681,0.00027641,-0.01455127,0.01029175,-0.0963628,-0.04627139,0.04450349,0.0174325,0.00814892,0.02008868,0.03310176,0.02264204,-0.02809587,-0.04598468,0.03642821,0.00878364,-0.02107454,0.02067385,0.06092161,-0.01921801,0.06999049,0.05950698,-0.02053096,0.01402278,-0.00400986,0.06074582,0.01157277,0.07150757,0.00858538,0.06798867,-0.00129285,-0.02388648,0.04752227,-0.02053337,-0.03745046,0.01479794,0.02016428,-0.03636533,-0.08763713,-0.08321965,0.04054644,-0.01517046,0.00894495,-0.01568125,0.01725661,-0.03596569,-0.05971939,-0.07996917,-0.04165657,0.00538678,-0.01824465,-0.0503631,0.01537544,0.00728943,-0.03263391,0.02230864,-0.02764091,-0.01552634,-0.05932111,0.04781163,-0.00240684,-0.0350192,-0.00225706,0.01394639,-0.03141247,0.07707902,-0.01693672,0.03143742,-0.03524111,0.04336641,-0.02628992,-0.03201673,0.03383823,0.03115782,0.01878017,-0.0434361,-0.0371987,0.02449102,-0.0388744,0.0142645,0.02235817,0.00636133,0.08250603,-0.0276214,-0.03183857,-0.00170724,0.00206445,-0.06008369,-0.05845115,-0.02892087,0.00904147,0.01187515,-0.0459939,0.04826059,0.05956094,0.02067597,0.03951092,0.01365309,-0.06828783,-0.01470881,-0.02252222,0.01032137,0.00359368,0.03871722,-0.01542242,-0.0318893,-0.05266765,0.03344322,-0.02884835,-0.04937479,-0.00587473,0.00794138,-0.05694033,0.0101214,-0.06065017,-0.0323976,0.0227886,0.04676826,0.01639635,-0.05724974,-0.00929836,-0.00614532,0.0412077,0.04860356,-0.01967224,0.02392195,0.02158416,0.00154852,0.05088983,0.02537197,-0.01165008,0.0357118,-0.0528552,0.04038453,-0.01475946,0.04602467,0.04985886,-0.00999867,-0.01022276,-0.00444744,0.0353791,0.05123522,-0.00548211,-0.00726091,0.02843998,0.00939019,-0.08654636,-0.05531484,-0.02377323,-0.0983156,0.04946027,0.01594926,0.01633087,-0.00470492,-0.03413393,-0.02148524,0.0028755,0.01296014,0.07567324,0.00955635,0.01335717,0.04162617,0.01178119,0.03942703,0.01056101,-0.05237014,0.02239005,0.00323957,-0.02694289,0.02313163,-0.03493944,-0.06638058,0.02991039,0.01952416,0.00884278,-0.00905617,0.01678033,0.03917867,0.01531709,-0.00935544,-0.06283118,0.02779017,-0.01242504,0.00663292,-0.00380109,0.02085857,-0.0382617,-0.02713603,0.03624997,-0.02254467,0.02779819,-0.0056603,-0.02425535,0.07110921,0.02050285,0.0050937,-0.01877945,0.04303421,-0.02939506,0.02685325,-0.02748824,-0.09057247,-0.03507509,0.01653716,-0.0121703,-0.01103431,0.06214304,-0.03172911,-0.00956099,0.02011253,0.00857208,-0.04824607,-0.01508945,0.04879459,0.01976893,0.01337735,0.03709561,0.01201214,0.02157417,0.05022896,0.01393114,-0.02179612,-0.04074711,0.03187118,0.06073371,-0.01211748,0.03477083,-0.00205375,0.02384684,-0.0102078,0.04296339,0.0388917,-0.04178028,-0.01995006,0.03940324,0.00380413,-0.05144774,-0.01176262,-0.06168364,0.00221204,-0.02693029,-0.03385232,0.00946134,0.06620699,0.04501926,-0.04840444,-0.03730004,0.04357322,-0.0526091,-0.01866001,0.01807246,0.02910393,0.00157543,-0.00745221,0.01056055,-0.04169816,0.01092786,-0.04197936,-0.03292974,-0.02505302,0.006578,-0.00317754,0.0440086,-0.02572477,-0.00074424,-0.02167999,0.00624407,0.06342509,-0.04652986,0.01294278,-0.0106153,-0.03509802,0.06060641,0.01891718,-0.00250467,0.05344574,-0.06447006,-0.02832588,-0.07019958,-0.00727558,0.00276726,-0.02400902,0.02392795,-0.0369478,-0.0294074,-0.0298551,-0.05222633,-0.0077395,-0.01405687,0.01936233,0.00585941,-0.00431646,-0.01191694,0.0199367,-0.03736813,-0.00939042,-0.01754024,0.04896359,-0.02276293,0.04567419,0.0194976,0.01131666,-0.0523431,-0.02026508,-0.01483221,0.03762119,-0.00376431,-0.06044089,-0.05320703,-0.04086602,0.02533652,-0.01696847,0.013495,0.02667199,-0.01809527,-0.01830807,0.00199135,-0.03552759,-0.00394709,0.02522177,-0.02572727,-0.00366035,-0.0300186,0.01605547,0.13505879,-0.00659076,-0.09600563,-0.06581681,-0.05175186,-0.00158923,0.02418906,0.00827223,-0.05937091,0.02410935,-0.01484752,-0.06765573,-0.01834817,-0.03877489,-0.01372095,-0.01215518,0.04245747,-0.02744206,-0.01152781,0.03111561,0.06169713,-0.05726384,0.06993216,0.02410406,-0.03953952,-0.02547003,-0.00797583,0.01216903,-0.03550759,0.03168,0.00811089,0.01671986,-0.01815159,0.03718624,0.01523675,-0.02176326,0.02151506,0.04419649,0.02238268,-0.05285096,-0.03016103,-0.03951897,-0.01259776,-0.0867182,-0.02274357,0.00908939,-0.00709404,-0.00794386,-0.08122491,-0.04728742,-0.02571099,-0.01833307,-0.05763094,-0.0186317,0.00549051,-0.01712245,-0.01615259,-0.00069676,-0.01917082,0.02528296,0.0494528,-0.01433723,-0.01922292,0.01937451,-0.01969154,-0.00523916,-0.01112253,0.05212815,0.00570255,0.01344375,0.01815151,0.01869245,-0.03524471,-0.00181231,-0.04211661,-0.05569287,0.05170191,-0.00925745,0.01103923,-0.01684693,-0.00390047,0.00641593,0.0219334,0.04770533,-0.01343497,0.00261723,0.02795079,0.02070889,0.0328014,0.01250408,-0.0912431,-0.00157842,-0.04763522,-0.01133074,0.01710939,-0.0178187,0.02064659,-0.01855401,0.03608721,-0.00386239,-0.03139237,0.02324245,-0.01833713,-0.01578792,-0.05722929,-0.00974983,-0.02849773,0.01839022,-0.00366862,0.02563846,-0.01459514,0.0068325,0.01086322,0.00985584,-0.00985949,0.04265652,-0.02323682,0.01204222,0.00355609,0.05274227,-0.04004765,0.04217076,-0.00629112,0.05110945,-0.02709708,0.03247368,-0.00507342,0.00213403,-0.03589604,-0.00749233,-0.01336735,-0.01403513,-0.01889321,0.05188816,-0.01583812,0.0593082,-0.00103319,-0.01559343,0.07755482,0.0806832,-0.01763302,-0.03391062,0.05229073,0.03812193,-0.01774237,0.00062796,0.04737424,-0.00425581,0.02926566,0.02382747,-0.03171533,0.09675542,0.06937278,-0.03724505,0.01554714,-0.01853221,0.01361442,0.01853951,0.04846149,-0.04788091,-0.0086635,0.02384683,-0.03505094,0.04160007,-0.04342892,0.05367236,-0.00823172,0.00664859,0.06291641,-0.02709524,-0.0064078,0.06195882,-0.01387455,0.03340103,0.00026883,-0.04047184,-0.03868345,0.00134266,0.00698534,-0.06864757,-0.03152622,0.0825591,0.00682236,-0.00064783,0.06020293,0.00943785,0.02533498,-0.02166454,-0.03601572,-0.0370335,0.02169916,0.03043843,-0.18327405,-0.03167285,0.02200961,0.03432247,-0.02884356,-0.03294496,-0.02402498,0.02230975,0.03341258,-0.0061008,0.01529306,-0.01538006,0.08327408,-0.01366107,-0.01307775,0.00371203,0.02010621,0.04550488,-0.0107622,0.036387,0.07094416,-0.03669319,-0.02378553,-0.00419444,0.03371866,0.08134218,-0.03864424,0.03131742,-0.01742758,-0.02368227,0.0262518,-0.01123927,-0.04425244,-0.03469411,0.03765458,-0.00185319,0.01230146,-0.02069914,-0.03872865,-0.02840627,0.05436758,0.08529447,-0.00797626,0.01001665,0.02419196,-0.03955293,-0.03118744,-0.06658254,0.04956432,0.00882588,-0.03675301,-0.04471878,-0.02765362,0.00485611,-0.00471341,-0.02894133,0.04571808,0.03236741,-0.00837033,0.03214405,0.00518269,0.02762478,-0.03993507,0.01520211,0.04154276,-0.00747077,0.00377446,-0.02216355,0.00145694,-0.02570479,0.02535607,-0.07006571,-0.0086783,0.00758225,0.01160703,-0.00883803,-0.07230768,-0.06152896,-0.00855331,0.10066069,0.05481771,0.01435771,0.01550363,-0.04230723,0.01249804,-0.00994475,0.0283228,0.00979971,-0.04887428,0.03097194,-0.04766491,-0.01203888,0.02888785,0.05890112,-0.03966148,-0.03932316,0.0631832,0.00463489,-0.01718206,-0.02964552,0.01466717,-0.04570083,0.00536609,0.0281515,0.0080071,0.01613442,0.02840873,0.03742357,-0.04892595,-0.04526094,-0.06880709,-0.0199977,-0.00676982,0.03134029,0.0051464,-0.01710495,-0.01359027,-0.0093915,-0.00295349,-0.0307786,-0.04460856,0.01570436,0.00590847,-0.00347664,0.03874956,-0.03486094,-0.0116695,0.07548875,-0.04923653,-0.02693233,-0.03416745,0.00142499,0.01322008,0.05684903,-0.00270093,0.00752953,-0.00171507,-0.00190167,0.01480245,0.03168093,-0.01774243,0.02007984,-0.01011267,-0.0283488,0.02552264,0.01997334,0.01454496,0.03427382,-0.04453989,-0.00442358,0.00533621,0.05255299,0.08415128,0.06964754,-0.07713536,-0.04323215,-0.0314332,-0.00052889,-0.00975278,0.01950345,0.00611715,-0.05359494,-0.00475607,0.02416224,0.06282063,-0.01437324,0.02491151,0.0122382,0.06976674,-0.01151685,0.0536018,0.01207243,0.02823494,-0.04545299,-0.0006713,0.07505135,-0.02527587,0.03510783,0.02734142,0.0013562,0.03846449,-0.01247979,-0.05989451,0.08331478,-0.07256109,-0.04242129,0.0117902,0.0258505,9.8e-7,-0.00282563,0.01775033,-0.00811497,-0.01323493,0.01211854,-0.04513775,-0.05499362,0.07388799,0.0078321],"last_embed":{"tokens":392,"hash":"1x6d3ti"}}},"last_read":{"hash":"1x6d3ti","at":1750826608894},"class_name":"SmartSource","outlinks":[{"title":"NFS","target":"NFS","line":18},{"title":"NFS","target":"NFS","line":18},{"title":"rpcbind","target":"rpcbind","line":18}],"metadata":{"aliases":["Remote Procedure Call"],"tags":["计算机网络/OSI模型/应用层"],"发布时间":null,"类型":["网络协议"],"文档更新日期":"2024-01-22 15:31","英文":"Remote Procedure Call","协议层级":["应用层"],"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,15],"#简介":[17,26],"#简介#{1}":[18,19],"#简介#{2}":[20,23],"#简介#{3}":[24,26],"#在 Linux上使用NFS挂载：":[27,63],"#在 Linux上使用NFS挂载：#{1}":[29,31],"#在 Linux上使用NFS挂载：#{2}":[32,39],"#在 Linux上使用NFS挂载：#{3}":[33,39],"#在 Linux上使用NFS挂载：#{4}":[40,47],"#在 Linux上使用NFS挂载：#{5}":[42,47],"#在 Linux上使用NFS挂载：#{6}":[48,55],"#在 Linux上使用NFS挂载：#{7}":[50,55],"#在 Linux上使用NFS挂载：#{8}":[56,63],"#在 Linux上使用NFS挂载：#{9}":[58,63]},"last_import":{"mtime":1740326885819,"size":1279,"at":1749024987637,"hash":"1x6d3ti"},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md","last_embed":{"hash":"1x6d3ti","at":1750826608894}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#---frontmatter---","lines":[1,15],"size":181,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#简介","lines":[17,26],"size":183,"outlinks":[{"title":"NFS","target":"NFS","line":2},{"title":"NFS","target":"NFS","line":2},{"title":"rpcbind","target":"rpcbind","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#简介#{1}","lines":[18,19],"size":101,"outlinks":[{"title":"NFS","target":"NFS","line":1},{"title":"NFS","target":"NFS","line":1},{"title":"rpcbind","target":"rpcbind","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#简介#{2}","lines":[20,23],"size":69,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#简介#{3}","lines":[24,26],"size":5,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#在 Linux上使用NFS挂载：": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#在 Linux上使用NFS挂载：","lines":[27,63],"size":453,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#在 Linux上使用NFS挂载：#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#在 Linux上使用NFS挂载：#{1}","lines":[29,31],"size":68,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#在 Linux上使用NFS挂载：#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#在 Linux上使用NFS挂载：#{2}","lines":[32,39],"size":88,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#在 Linux上使用NFS挂载：#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#在 Linux上使用NFS挂载：#{3}","lines":[33,39],"size":71,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#在 Linux上使用NFS挂载：#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#在 Linux上使用NFS挂载：#{4}","lines":[40,47],"size":62,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#在 Linux上使用NFS挂载：#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#在 Linux上使用NFS挂载：#{5}","lines":[42,47],"size":45,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#在 Linux上使用NFS挂载：#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#在 Linux上使用NFS挂载：#{6}","lines":[48,55],"size":83,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#在 Linux上使用NFS挂载：#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#在 Linux上使用NFS挂载：#{7}","lines":[50,55],"size":64,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#在 Linux上使用NFS挂载：#{8}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#在 Linux上使用NFS挂载：#{8}","lines":[56,63],"size":128,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#在 Linux上使用NFS挂载：#{9}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md#在 Linux上使用NFS挂载：#{9}","lines":[58,63],"size":73,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07163137,-0.02718851,0.00032567,-0.05419776,-0.00740623,-0.01488455,-0.0200272,0.03159269,0.03783019,0.0202372,0.00436091,0.00157314,0.07526983,0.0482422,0.069562,0.00372336,-0.01450499,0.03212509,0.00461339,0.04799471,0.0827103,-0.02104541,-0.02749822,-0.05375473,0.02664329,0.04101408,0.0031888,-0.0242663,-0.0101897,-0.15931202,0.01912205,0.02196709,0.00017729,0.03354497,0.00259966,0.00428961,0.0266999,0.02455757,-0.03358293,0.01398993,-0.00170538,-0.05451404,0.01152026,-0.01224824,-0.02842634,-0.08694559,-0.01445655,-0.04260777,0.05534267,-0.01735439,-0.0606499,-0.04355598,-0.07881957,-0.02432081,-0.00128716,-0.03805584,0.051472,0.02071131,0.08482765,-0.07223813,0.04474401,0.04300618,-0.2276973,0.06891785,0.02664104,-0.02858536,0.00709375,-0.0342214,0.04088838,-0.0023585,-0.04771248,0.0510856,-0.02008607,0.06784165,0.04021601,-0.0277408,-0.01505398,-0.00742072,-0.01624909,-0.05800552,-0.03518982,0.02515552,-0.07124358,-0.01104528,0.0186721,0.03094389,-0.03171563,-0.01227349,0.02710673,0.0082781,-0.02681168,-0.01365603,0.04337997,0.02599608,-0.04826013,0.01927901,0.03698268,0.07130962,-0.07694581,0.10465477,-0.03541133,0.00223473,-0.0312371,-0.06644001,0.01793297,-0.04578814,-0.03242393,-0.01197274,-0.03696417,0.09547403,-0.04347008,-0.08087777,0.03962507,-0.05749781,0.04769315,0.05500964,0.00753376,0.03300015,-0.0498592,0.00452762,-0.04315956,0.00816617,0.07696231,-0.00726368,-0.01050537,-0.05579907,0.0479189,0.05015337,0.02507956,0.03803283,0.06580661,-0.01883416,-0.01763537,-0.0167771,-0.01116839,0.02681391,0.00552446,-0.01020866,-0.03258677,-0.03338793,-0.01310145,-0.06281965,0.01299768,-0.05912506,-0.03314607,0.06786524,-0.06372102,0.03394962,0.00376679,-0.05867043,0.04094608,0.06047456,-0.00596894,-0.04490224,-0.04589942,-0.0012257,0.07218514,0.13061912,-0.04556448,-0.0071204,0.00285765,-0.03924634,-0.05204318,0.12942807,0.05491962,-0.10675576,-0.00916232,-0.00727107,0.0099781,-0.01854977,0.03979147,-0.03461765,0.05021388,-0.05791024,0.04009612,0.00033068,-0.03529773,-0.03205688,0.02722782,-0.00759933,0.03097414,-0.02879078,-0.03489409,-0.0070398,-0.00184075,-0.08998224,-0.04367487,-0.02189085,0.02071555,-0.06412486,-0.10210832,0.01039425,-0.02496733,0.02330518,-0.00067756,-0.0412196,0.0322523,-0.00892319,0.03105534,-0.00394023,0.07992398,0.04367603,-0.01716519,-0.04933836,-0.06836864,-0.03250279,-0.00191051,0.04024559,0.0141183,0.07839684,0.01744754,0.05827929,0.00821667,0.05907299,-0.08358327,0.03572579,0.00748542,0.04020009,0.07750349,0.05339228,0.02808439,0.03555338,-0.06078142,-0.22464748,0.00645975,0.0585821,-0.03496764,0.03177192,-0.00070154,0.0500892,0.03504124,0.05012287,0.04909883,0.1433952,0.02564633,-0.03911418,-0.0021692,0.02439426,0.00704581,0.06192805,-0.03416543,-0.0648426,0.02069134,-0.01654231,0.0503748,-0.02561714,0.0425226,0.06143333,-0.01019974,0.10089099,-0.00828881,0.06531645,-0.00744723,0.08364901,-0.00631533,0.01414845,-0.12753706,-0.02972311,0.054813,-0.00906515,0.02078066,0.01423692,0.00628092,-0.03601383,0.04669692,-0.00399878,-0.08571418,-0.00023525,-0.032789,-0.04681602,-0.0107196,0.02790372,0.00483433,-0.00036148,0.00772331,0.02162099,-0.02240322,0.02007064,-0.04205516,-0.04628031,-0.05952746,0.01634568,0.03364267,0.05801874,0.01328706,0.00263008,-0.00203464,0.0308846,-0.07142828,0.02621337,0.03458304,-0.00189923,-0.03062774,-0.02782252,0.13329442,0.0084391,0.00259172,0.03158793,-0.0358532,-0.00125228,-0.06069812,-0.01540616,-0.05029972,0.07060098,-0.04432099,0.01126899,-0.02042825,-0.00756115,-0.00871792,0.01759313,0.01223582,0.03181694,-0.06385096,-0.0484329,-0.00797561,-0.0396921,0.05152902,0.0585348,0.00486723,-0.30395898,-0.00099236,-0.04112466,-0.00531034,0.02111336,0.02505792,0.06549044,-0.01212064,-0.0638779,0.03004126,-0.06393146,0.0668633,-0.03446285,-0.02360511,0.00886813,0.00420259,0.06084689,0.00506837,0.03110166,-0.08646768,-0.01653787,0.06291728,0.1847185,-0.00818782,0.08204412,0.03339183,-0.02175678,0.03429126,0.0195136,0.03822045,0.00542489,-0.04996029,-0.03510289,-0.08636104,0.05673297,0.04034197,0.00981909,0.01172953,0.01697414,-0.03244792,-0.05494159,0.05876148,-0.01737483,0.00404276,0.09839251,-0.01116305,-0.03076928,-0.03738828,0.05141411,0.07362703,-0.00382385,0.05928607,0.01046534,-0.01538008,0.03055319,0.02603669,0.02777484,-0.04303941,-0.0902504,-0.01648292,0.02355342,-0.03866062,0.10642567,0.05731089,0.0465605],"last_embed":{"hash":"d5e863e3b80f35a1cb72d5935628016c81cea934c7f1cd350cc45de5da470036","tokens":463}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.08668999,-0.00157813,0.01850887,0.00211984,0.00740805,0.0188796,0.01787624,-0.03235561,0.00245754,-0.01189781,0.01579633,0.03235987,-0.01910622,-0.05705259,0.01267158,0.04458245,-0.02009462,0.01151659,0.04086521,-0.03327732,-0.00696689,-0.02215198,-0.00966235,-0.00460994,-0.00493853,0.03394785,-0.04675266,-0.04976302,0.00422675,0.015836,0.05494194,-0.01823898,0.05626711,-0.01909724,0.00442577,0.02956172,0.00052085,0.02358423,-0.00346811,-0.03298507,-0.01439714,-0.00147854,-0.0297751,0.02072363,-0.09010463,0.00598548,0.01558194,0.0053896,0.00470762,0.02246787,0.02027106,0.03961234,0.01205396,-0.03459041,0.0171681,0.00027641,-0.01455127,0.01029175,-0.0963628,-0.04627139,0.04450349,0.0174325,0.00814892,0.02008868,0.03310176,0.02264204,-0.02809587,-0.04598468,0.03642821,0.00878364,-0.02107454,0.02067385,0.06092161,-0.01921801,0.06999049,0.05950698,-0.02053096,0.01402278,-0.00400986,0.06074582,0.01157277,0.07150757,0.00858538,0.06798867,-0.00129285,-0.02388648,0.04752227,-0.02053337,-0.03745046,0.01479794,0.02016428,-0.03636533,-0.08763713,-0.08321965,0.04054644,-0.01517046,0.00894495,-0.01568125,0.01725661,-0.03596569,-0.05971939,-0.07996917,-0.04165657,0.00538678,-0.01824465,-0.0503631,0.01537544,0.00728943,-0.03263391,0.02230864,-0.02764091,-0.01552634,-0.05932111,0.04781163,-0.00240684,-0.0350192,-0.00225706,0.01394639,-0.03141247,0.07707902,-0.01693672,0.03143742,-0.03524111,0.04336641,-0.02628992,-0.03201673,0.03383823,0.03115782,0.01878017,-0.0434361,-0.0371987,0.02449102,-0.0388744,0.0142645,0.02235817,0.00636133,0.08250603,-0.0276214,-0.03183857,-0.00170724,0.00206445,-0.06008369,-0.05845115,-0.02892087,0.00904147,0.01187515,-0.0459939,0.04826059,0.05956094,0.02067597,0.03951092,0.01365309,-0.06828783,-0.01470881,-0.02252222,0.01032137,0.00359368,0.03871722,-0.01542242,-0.0318893,-0.05266765,0.03344322,-0.02884835,-0.04937479,-0.00587473,0.00794138,-0.05694033,0.0101214,-0.06065017,-0.0323976,0.0227886,0.04676826,0.01639635,-0.05724974,-0.00929836,-0.00614532,0.0412077,0.04860356,-0.01967224,0.02392195,0.02158416,0.00154852,0.05088983,0.02537197,-0.01165008,0.0357118,-0.0528552,0.04038453,-0.01475946,0.04602467,0.04985886,-0.00999867,-0.01022276,-0.00444744,0.0353791,0.05123522,-0.00548211,-0.00726091,0.02843998,0.00939019,-0.08654636,-0.05531484,-0.02377323,-0.0983156,0.04946027,0.01594926,0.01633087,-0.00470492,-0.03413393,-0.02148524,0.0028755,0.01296014,0.07567324,0.00955635,0.01335717,0.04162617,0.01178119,0.03942703,0.01056101,-0.05237014,0.02239005,0.00323957,-0.02694289,0.02313163,-0.03493944,-0.06638058,0.02991039,0.01952416,0.00884278,-0.00905617,0.01678033,0.03917867,0.01531709,-0.00935544,-0.06283118,0.02779017,-0.01242504,0.00663292,-0.00380109,0.02085857,-0.0382617,-0.02713603,0.03624997,-0.02254467,0.02779819,-0.0056603,-0.02425535,0.07110921,0.02050285,0.0050937,-0.01877945,0.04303421,-0.02939506,0.02685325,-0.02748824,-0.09057247,-0.03507509,0.01653716,-0.0121703,-0.01103431,0.06214304,-0.03172911,-0.00956099,0.02011253,0.00857208,-0.04824607,-0.01508945,0.04879459,0.01976893,0.01337735,0.03709561,0.01201214,0.02157417,0.05022896,0.01393114,-0.02179612,-0.04074711,0.03187118,0.06073371,-0.01211748,0.03477083,-0.00205375,0.02384684,-0.0102078,0.04296339,0.0388917,-0.04178028,-0.01995006,0.03940324,0.00380413,-0.05144774,-0.01176262,-0.06168364,0.00221204,-0.02693029,-0.03385232,0.00946134,0.06620699,0.04501926,-0.04840444,-0.03730004,0.04357322,-0.0526091,-0.01866001,0.01807246,0.02910393,0.00157543,-0.00745221,0.01056055,-0.04169816,0.01092786,-0.04197936,-0.03292974,-0.02505302,0.006578,-0.00317754,0.0440086,-0.02572477,-0.00074424,-0.02167999,0.00624407,0.06342509,-0.04652986,0.01294278,-0.0106153,-0.03509802,0.06060641,0.01891718,-0.00250467,0.05344574,-0.06447006,-0.02832588,-0.07019958,-0.00727558,0.00276726,-0.02400902,0.02392795,-0.0369478,-0.0294074,-0.0298551,-0.05222633,-0.0077395,-0.01405687,0.01936233,0.00585941,-0.00431646,-0.01191694,0.0199367,-0.03736813,-0.00939042,-0.01754024,0.04896359,-0.02276293,0.04567419,0.0194976,0.01131666,-0.0523431,-0.02026508,-0.01483221,0.03762119,-0.00376431,-0.06044089,-0.05320703,-0.04086602,0.02533652,-0.01696847,0.013495,0.02667199,-0.01809527,-0.01830807,0.00199135,-0.03552759,-0.00394709,0.02522177,-0.02572727,-0.00366035,-0.0300186,0.01605547,0.13505879,-0.00659076,-0.09600563,-0.06581681,-0.05175186,-0.00158923,0.02418906,0.00827223,-0.05937091,0.02410935,-0.01484752,-0.06765573,-0.01834817,-0.03877489,-0.01372095,-0.01215518,0.04245747,-0.02744206,-0.01152781,0.03111561,0.06169713,-0.05726384,0.06993216,0.02410406,-0.03953952,-0.02547003,-0.00797583,0.01216903,-0.03550759,0.03168,0.00811089,0.01671986,-0.01815159,0.03718624,0.01523675,-0.02176326,0.02151506,0.04419649,0.02238268,-0.05285096,-0.03016103,-0.03951897,-0.01259776,-0.0867182,-0.02274357,0.00908939,-0.00709404,-0.00794386,-0.08122491,-0.04728742,-0.02571099,-0.01833307,-0.05763094,-0.0186317,0.00549051,-0.01712245,-0.01615259,-0.00069676,-0.01917082,0.02528296,0.0494528,-0.01433723,-0.01922292,0.01937451,-0.01969154,-0.00523916,-0.01112253,0.05212815,0.00570255,0.01344375,0.01815151,0.01869245,-0.03524471,-0.00181231,-0.04211661,-0.05569287,0.05170191,-0.00925745,0.01103923,-0.01684693,-0.00390047,0.00641593,0.0219334,0.04770533,-0.01343497,0.00261723,0.02795079,0.02070889,0.0328014,0.01250408,-0.0912431,-0.00157842,-0.04763522,-0.01133074,0.01710939,-0.0178187,0.02064659,-0.01855401,0.03608721,-0.00386239,-0.03139237,0.02324245,-0.01833713,-0.01578792,-0.05722929,-0.00974983,-0.02849773,0.01839022,-0.00366862,0.02563846,-0.01459514,0.0068325,0.01086322,0.00985584,-0.00985949,0.04265652,-0.02323682,0.01204222,0.00355609,0.05274227,-0.04004765,0.04217076,-0.00629112,0.05110945,-0.02709708,0.03247368,-0.00507342,0.00213403,-0.03589604,-0.00749233,-0.01336735,-0.01403513,-0.01889321,0.05188816,-0.01583812,0.0593082,-0.00103319,-0.01559343,0.07755482,0.0806832,-0.01763302,-0.03391062,0.05229073,0.03812193,-0.01774237,0.00062796,0.04737424,-0.00425581,0.02926566,0.02382747,-0.03171533,0.09675542,0.06937278,-0.03724505,0.01554714,-0.01853221,0.01361442,0.01853951,0.04846149,-0.04788091,-0.0086635,0.02384683,-0.03505094,0.04160007,-0.04342892,0.05367236,-0.00823172,0.00664859,0.06291641,-0.02709524,-0.0064078,0.06195882,-0.01387455,0.03340103,0.00026883,-0.04047184,-0.03868345,0.00134266,0.00698534,-0.06864757,-0.03152622,0.0825591,0.00682236,-0.00064783,0.06020293,0.00943785,0.02533498,-0.02166454,-0.03601572,-0.0370335,0.02169916,0.03043843,-0.18327405,-0.03167285,0.02200961,0.03432247,-0.02884356,-0.03294496,-0.02402498,0.02230975,0.03341258,-0.0061008,0.01529306,-0.01538006,0.08327408,-0.01366107,-0.01307775,0.00371203,0.02010621,0.04550488,-0.0107622,0.036387,0.07094416,-0.03669319,-0.02378553,-0.00419444,0.03371866,0.08134218,-0.03864424,0.03131742,-0.01742758,-0.02368227,0.0262518,-0.01123927,-0.04425244,-0.03469411,0.03765458,-0.00185319,0.01230146,-0.02069914,-0.03872865,-0.02840627,0.05436758,0.08529447,-0.00797626,0.01001665,0.02419196,-0.03955293,-0.03118744,-0.06658254,0.04956432,0.00882588,-0.03675301,-0.04471878,-0.02765362,0.00485611,-0.00471341,-0.02894133,0.04571808,0.03236741,-0.00837033,0.03214405,0.00518269,0.02762478,-0.03993507,0.01520211,0.04154276,-0.00747077,0.00377446,-0.02216355,0.00145694,-0.02570479,0.02535607,-0.07006571,-0.0086783,0.00758225,0.01160703,-0.00883803,-0.07230768,-0.06152896,-0.00855331,0.10066069,0.05481771,0.01435771,0.01550363,-0.04230723,0.01249804,-0.00994475,0.0283228,0.00979971,-0.04887428,0.03097194,-0.04766491,-0.01203888,0.02888785,0.05890112,-0.03966148,-0.03932316,0.0631832,0.00463489,-0.01718206,-0.02964552,0.01466717,-0.04570083,0.00536609,0.0281515,0.0080071,0.01613442,0.02840873,0.03742357,-0.04892595,-0.04526094,-0.06880709,-0.0199977,-0.00676982,0.03134029,0.0051464,-0.01710495,-0.01359027,-0.0093915,-0.00295349,-0.0307786,-0.04460856,0.01570436,0.00590847,-0.00347664,0.03874956,-0.03486094,-0.0116695,0.07548875,-0.04923653,-0.02693233,-0.03416745,0.00142499,0.01322008,0.05684903,-0.00270093,0.00752953,-0.00171507,-0.00190167,0.01480245,0.03168093,-0.01774243,0.02007984,-0.01011267,-0.0283488,0.02552264,0.01997334,0.01454496,0.03427382,-0.04453989,-0.00442358,0.00533621,0.05255299,0.08415128,0.06964754,-0.07713536,-0.04323215,-0.0314332,-0.00052889,-0.00975278,0.01950345,0.00611715,-0.05359494,-0.00475607,0.02416224,0.06282063,-0.01437324,0.02491151,0.0122382,0.06976674,-0.01151685,0.0536018,0.01207243,0.02823494,-0.04545299,-0.0006713,0.07505135,-0.02527587,0.03510783,0.02734142,0.0013562,0.03846449,-0.01247979,-0.05989451,0.08331478,-0.07256109,-0.04242129,0.0117902,0.0258505,9.8e-7,-0.00282563,0.01775033,-0.00811497,-0.01323493,0.01211854,-0.04513775,-0.05499362,0.07388799,0.0078321],"last_embed":{"tokens":392,"hash":"1x6d3ti"}}},"last_read":{"hash":"1x6d3ti","at":1750903850645},"class_name":"SmartSource","outlinks":[{"title":"NFS","target":"NFS","line":18},{"title":"NFS","target":"NFS","line":18},{"title":"rpcbind","target":"rpcbind","line":18}],"metadata":{"aliases":["Remote Procedure Call"],"tags":["计算机网络/OSI模型/应用层"],"发布时间":null,"类型":["网络协议"],"文档更新日期":"2024-01-22 15:31","英文":"Remote Procedure Call","协议层级":["应用层"],"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,15],"#简介":[17,26],"#简介#{1}":[18,19],"#简介#{2}":[20,23],"#简介#{3}":[24,26],"#在 Linux上使用NFS挂载：":[27,63],"#在 Linux上使用NFS挂载：#{1}":[29,31],"#在 Linux上使用NFS挂载：#{2}":[32,39],"#在 Linux上使用NFS挂载：#{3}":[33,39],"#在 Linux上使用NFS挂载：#{4}":[40,47],"#在 Linux上使用NFS挂载：#{5}":[42,47],"#在 Linux上使用NFS挂载：#{6}":[48,55],"#在 Linux上使用NFS挂载：#{7}":[50,55],"#在 Linux上使用NFS挂载：#{8}":[56,63],"#在 Linux上使用NFS挂载：#{9}":[58,63]},"last_import":{"mtime":1740326885819,"size":1279,"at":1749024987637,"hash":"1x6d3ti"},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/RPC协议.md","last_embed":{"hash":"1x6d3ti","at":1750903850645}},